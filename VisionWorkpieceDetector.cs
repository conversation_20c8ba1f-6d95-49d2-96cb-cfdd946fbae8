using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using VM.Core;
using VM.PlatformSDKCS;
using ImageSourceModuleCs;
using IMVSCircleFindModuCs;
using IMVSFastFeatureMatchModuCs;
using IMVSGrayMatchModuCs;

namespace VisionWorkpieceDetection
{
    /// <summary>
    /// 基于DobotVisionStudio的工件视觉识别检测器
    /// 集成了圆形检测、特征匹配、灰度匹配等核心算法
    /// </summary>
    public class VisionWorkpieceDetector
    {
        private bool _solutionLoaded = false;
        private string _solutionPath;
        
        public event Action<string> OnLogMessage;
        public event Action<DetectionResult> OnDetectionComplete;

        /// <summary>
        /// 检测结果数据结构
        /// </summary>
        public class DetectionResult
        {
            public bool Success { get; set; }
            public double CenterX { get; set; }
            public double CenterY { get; set; }
            public double Radius { get; set; }
            public double Angle { get; set; }
            public double MatchScore { get; set; }
            public string WorkpieceType { get; set; }
            public DateTime DetectionTime { get; set; }
        }

        /// <summary>
        /// 初始化检测器
        /// </summary>
        /// <param name="solutionPath">VisionMaster方案文件路径</param>
        /// <param name="password">方案密码</param>
        public bool Initialize(string solutionPath, string password = "")
        {
            try
            {
                _solutionPath = solutionPath;
                
                // 加载VisionMaster方案
                VmSolution.Import(solutionPath, password);
                _solutionLoaded = true;
                
                LogMessage("视觉检测器初始化成功");
                return true;
            }
            catch (VmException ex)
            {
                LogMessage($"初始化失败: {ex.Message} (错误代码: {ex.errorCode:X})");
                return false;
            }
        }

        /// <summary>
        /// 执行圆形工件检测
        /// </summary>
        /// <param name="imagePath">图像文件路径</param>
        /// <returns>检测结果</returns>
        public DetectionResult DetectCircularWorkpiece(string imagePath)
        {
            var result = new DetectionResult
            {
                DetectionTime = DateTime.Now,
                WorkpieceType = "圆形工件"
            };

            if (!_solutionLoaded)
            {
                LogMessage("检测器未初始化");
                return result;
            }

            try
            {
                // 1. 设置图像源
                var imageSource = (ImageSourceModuleTool)VmSolution.Instance["流程1.图像源1"];
                if (imageSource != null && File.Exists(imagePath))
                {
                    // 设置图像路径
                    imageSource.ModuParams.ImagePath = imagePath;
                }

                // 2. 获取圆形检测模块
                var circleDetector = (IMVSCircleFindModuTool)VmSolution.Instance["流程1.圆查找1"];
                if (circleDetector == null)
                {
                    LogMessage("圆形检测模块未找到");
                    return result;
                }

                // 3. 执行检测流程
                var process = (VmProcedure)VmSolution.Instance["流程1"];
                process.Run();

                // 4. 获取检测结果
                var centerX = circleDetector.ModuResult.OutputCircle.CenterPoint.X;
                var centerY = circleDetector.ModuResult.OutputCircle.CenterPoint.Y;
                var radius = circleDetector.ModuResult.OutputCircle.Radius;
                
                // 获取模块状态
                var moduleStatus = (VmSolution.Instance["流程1.圆查找1.Outputs.ModuStatus.Value"] as Array)?.GetValue(0);
                bool detectionSuccess = moduleStatus?.ToString() == "1";

                result.Success = detectionSuccess;
                result.CenterX = centerX;
                result.CenterY = centerY;
                result.Radius = radius;

                LogMessage($"圆形检测完成 - 中心点:({centerX:F2}, {centerY:F2}), 半径:{radius:F2}");
                
                OnDetectionComplete?.Invoke(result);
                return result;
            }
            catch (Exception ex)
            {
                LogMessage($"圆形检测失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 执行快速特征匹配检测
        /// </summary>
        /// <param name="imagePath">图像文件路径</param>
        /// <returns>检测结果</returns>
        public DetectionResult DetectFeatureWorkpiece(string imagePath)
        {
            var result = new DetectionResult
            {
                DetectionTime = DateTime.Now,
                WorkpieceType = "特征工件"
            };

            if (!_solutionLoaded)
            {
                LogMessage("检测器未初始化");
                return result;
            }

            try
            {
                // 1. 设置图像源
                var imageSource = (ImageSourceModuleTool)VmSolution.Instance["流程1.图像源1"];
                if (imageSource != null && File.Exists(imagePath))
                {
                    imageSource.ModuParams.ImagePath = imagePath;
                }

                // 2. 获取快速特征匹配模块
                var featureMatcher = (IMVSFastFeatureMatchModuTool)VmSolution.Instance["流程1.快速匹配1"];
                if (featureMatcher == null)
                {
                    LogMessage("快速特征匹配模块未找到");
                    return result;
                }

                // 3. 执行检测流程
                var process = (VmProcedure)VmSolution.Instance["流程1"];
                process.Run();

                // 4. 获取匹配结果
                var matchResults = featureMatcher.ModuResult;
                if (matchResults.MatchBoxs.Count > 0)
                {
                    var firstMatch = matchResults.MatchBoxs[0];
                    result.Success = true;
                    result.CenterX = firstMatch.CenterPoint.X;
                    result.CenterY = firstMatch.CenterPoint.Y;
                    result.Angle = firstMatch.Angle;
                    result.MatchScore = firstMatch.Score;

                    LogMessage($"特征匹配成功 - 位置:({result.CenterX:F2}, {result.CenterY:F2}), 角度:{result.Angle:F2}°, 分数:{result.MatchScore:F3}");
                }
                else
                {
                    LogMessage("未找到匹配的特征");
                }

                OnDetectionComplete?.Invoke(result);
                return result;
            }
            catch (Exception ex)
            {
                LogMessage($"特征匹配检测失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 执行灰度模板匹配检测
        /// </summary>
        /// <param name="imagePath">图像文件路径</param>
        /// <returns>检测结果</returns>
        public DetectionResult DetectGrayTemplateWorkpiece(string imagePath)
        {
            var result = new DetectionResult
            {
                DetectionTime = DateTime.Now,
                WorkpieceType = "灰度模板工件"
            };

            if (!_solutionLoaded)
            {
                LogMessage("检测器未初始化");
                return result;
            }

            try
            {
                // 1. 设置图像源
                var imageSource = (ImageSourceModuleTool)VmSolution.Instance["流程1.图像源1"];
                if (imageSource != null && File.Exists(imagePath))
                {
                    imageSource.ModuParams.ImagePath = imagePath;
                }

                // 2. 获取灰度匹配模块
                var grayMatcher = (IMVSGrayMatchModuTool)VmSolution.Instance["流程1.灰度匹配1"];
                if (grayMatcher == null)
                {
                    LogMessage("灰度匹配模块未找到");
                    return result;
                }

                // 3. 执行检测流程
                var process = (VmProcedure)VmSolution.Instance["流程1"];
                process.Run();

                // 4. 获取匹配结果
                var matchResults = grayMatcher.ModuResult;
                if (matchResults.MatchBoxs.Count > 0)
                {
                    var firstMatch = matchResults.MatchBoxs[0];
                    result.Success = true;
                    result.CenterX = firstMatch.CenterPoint.X;
                    result.CenterY = firstMatch.CenterPoint.Y;
                    result.Angle = firstMatch.Angle;
                    result.MatchScore = firstMatch.Score;

                    LogMessage($"灰度匹配成功 - 位置:({result.CenterX:F2}, {result.CenterY:F2}), 角度:{result.Angle:F2}°, 分数:{result.MatchScore:F3}");
                }
                else
                {
                    LogMessage("未找到匹配的灰度模板");
                }

                OnDetectionComplete?.Invoke(result);
                return result;
            }
            catch (Exception ex)
            {
                LogMessage($"灰度匹配检测失败: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 配置圆形检测参数
        /// </summary>
        public void ConfigureCircleDetection(int edgeThreshold = 15, int edgeWidth = 1, 
            double minRadius = 10, double maxRadius = 500)
        {
            try
            {
                var circleDetector = (IMVSCircleFindModuTool)VmSolution.Instance["流程1.圆查找1"];
                if (circleDetector != null)
                {
                    var param = circleDetector.ModuParams;
                    // 设置边缘检测参数
                    // param.EdgeThresh = edgeThreshold;
                    // param.EdgeWidth = edgeWidth;
                    // param.RadiusLimitLow = minRadius;
                    // param.RadiusLimitHigh = maxRadius;
                    
                    LogMessage($"圆形检测参数已配置 - 边缘阈值:{edgeThreshold}, 半径范围:{minRadius}-{maxRadius}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"配置圆形检测参数失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 配置特征匹配参数
        /// </summary>
        public void ConfigureFeatureMatching(double minScore = 0.5, int maxMatchNum = 1)
        {
            try
            {
                var featureMatcher = (IMVSFastFeatureMatchModuTool)VmSolution.Instance["流程1.快速匹配1"];
                if (featureMatcher != null)
                {
                    var param = featureMatcher.ModuParams;
                    param.MinScore = minScore;
                    param.MaxMatchNum = maxMatchNum;
                    
                    LogMessage($"特征匹配参数已配置 - 最小分数:{minScore}, 最大匹配数:{maxMatchNum}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"配置特征匹配参数失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 将检测结果转换为机器人坐标格式
        /// </summary>
        public string ConvertToRobotCoordinates(DetectionResult result, double scaleX = 1.0, double scaleY = 1.0)
        {
            if (!result.Success)
                return "COORD:0,0,0";

            double robotX = result.CenterX * scaleX;
            double robotY = result.CenterY * scaleY;
            double robotR = result.Angle;

            return $"COORD:{robotX:F2},{robotY:F2},{robotR:F2}";
        }

        /// <summary>
        /// 将图像编码为Base64字符串用于传输
        /// </summary>
        public string EncodeImageToBase64(string imagePath, int jpegQuality = 80)
        {
            try
            {
                using (var image = Image.FromFile(imagePath))
                using (var ms = new MemoryStream())
                {
                    var jpegEncoder = GetJpegEncoder();
                    var qualityParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, jpegQuality);
                    var encoderParams = new EncoderParameters(1);
                    encoderParams.Param[0] = qualityParam;

                    image.Save(ms, jpegEncoder, encoderParams);
                    byte[] imageBytes = ms.ToArray();
                    string base64String = Convert.ToBase64String(imageBytes);
                    
                    return $"IMAGE:{base64String}";
                }
            }
            catch (Exception ex)
            {
                LogMessage($"图像编码失败: {ex.Message}");
                return "IMAGE:";
            }
        }

        private ImageCodecInfo GetJpegEncoder()
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageDecoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.FormatID == ImageFormat.Jpeg.Guid)
                    return codec;
            }
            return null;
        }

        /// <summary>
        /// 保存检测方案
        /// </summary>
        public bool SaveSolution(string password = "")
        {
            try
            {
                if (_solutionLoaded && !string.IsNullOrEmpty(_solutionPath))
                {
                    VmSolution.Export(_solutionPath, password);
                    LogMessage("方案保存成功");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LogMessage($"保存方案失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                if (_solutionLoaded)
                {
                    VmSolution.Instance?.Dispose();
                    _solutionLoaded = false;
                    LogMessage("视觉检测器已释放");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"释放资源时出错: {ex.Message}");
            }
        }

        private void LogMessage(string message)
        {
            OnLogMessage?.Invoke($"[{DateTime.Now:HH:mm:ss}] {message}");
        }
    }
}
