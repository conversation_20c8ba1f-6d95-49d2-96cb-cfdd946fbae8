#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DobotVisionStudio 工件识别演示程序

这个演示程序展示了如何使用DobotVisionStudio进行工件识别的完整流程：
1. 初始化视觉系统
2. 加载配置和模板
3. 执行工件检测
4. 坐标转换和机器人控制
5. 结果可视化和保存
"""

import cv2
import numpy as np
import json
import time
import os
from datetime import datetime
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading

from vision_workpiece_detector import VisionWorkpieceDetector

class VisionDemoGUI:
    """视觉检测演示GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DobotVisionStudio 工件识别演示")
        self.root.geometry("1200x800")
        
        # 初始化检测器
        self.detector = VisionWorkpieceDetector("vision_config.json")
        
        # 当前图像和结果
        self.current_image = None
        self.detection_results = []
        self.template_image = None
        
        self.setup_ui()
        self.setup_logging()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 图像显示区域
        image_frame = ttk.LabelFrame(main_frame, text="图像显示")
        image_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 设置控制面板
        self.setup_control_panel(control_frame)
        
        # 设置图像显示
        self.setup_image_display(image_frame)
        
    def setup_control_panel(self, parent):
        """设置控制面板"""
        # 文件操作
        file_frame = ttk.LabelFrame(parent, text="文件操作")
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(file_frame, text="加载图像", command=self.load_image).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="加载模板", command=self.load_template).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="保存结果", command=self.save_results).pack(fill=tk.X, pady=2)
        
        # 检测算法选择
        algo_frame = ttk.LabelFrame(parent, text="检测算法")
        algo_frame.pack(fill=tk.X, pady=5)
        
        self.algo_var = tk.StringVar(value="circle")
        ttk.Radiobutton(algo_frame, text="圆形检测", variable=self.algo_var, value="circle").pack(anchor=tk.W)
        ttk.Radiobutton(algo_frame, text="模板匹配", variable=self.algo_var, value="template").pack(anchor=tk.W)
        ttk.Radiobutton(algo_frame, text="边缘特征", variable=self.algo_var, value="edge").pack(anchor=tk.W)
        
        # 参数设置
        param_frame = ttk.LabelFrame(parent, text="参数设置")
        param_frame.pack(fill=tk.X, pady=5)
        
        # 圆形检测参数
        ttk.Label(param_frame, text="最小半径:").pack(anchor=tk.W)
        self.min_radius_var = tk.IntVar(value=10)
        ttk.Scale(param_frame, from_=5, to=50, variable=self.min_radius_var, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        ttk.Label(param_frame, text="最大半径:").pack(anchor=tk.W)
        self.max_radius_var = tk.IntVar(value=200)
        ttk.Scale(param_frame, from_=50, to=500, variable=self.max_radius_var, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        # 模板匹配参数
        ttk.Label(param_frame, text="匹配阈值:").pack(anchor=tk.W)
        self.match_threshold_var = tk.DoubleVar(value=0.7)
        ttk.Scale(param_frame, from_=0.1, to=1.0, variable=self.match_threshold_var, orient=tk.HORIZONTAL).pack(fill=tk.X)
        
        # 执行按钮
        action_frame = ttk.LabelFrame(parent, text="执行操作")
        action_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(action_frame, text="开始检测", command=self.start_detection).pack(fill=tk.X, pady=2)
        ttk.Button(action_frame, text="清除结果", command=self.clear_results).pack(fill=tk.X, pady=2)
        
        # TCP服务器控制
        tcp_frame = ttk.LabelFrame(parent, text="TCP服务器")
        tcp_frame.pack(fill=tk.X, pady=5)
        
        self.tcp_status_var = tk.StringVar(value="未启动")
        ttk.Label(tcp_frame, textvariable=self.tcp_status_var).pack()
        
        ttk.Button(tcp_frame, text="启动服务器", command=self.start_tcp_server).pack(fill=tk.X, pady=2)
        ttk.Button(tcp_frame, text="停止服务器", command=self.stop_tcp_server).pack(fill=tk.X, pady=2)
        
        # 结果显示
        result_frame = ttk.LabelFrame(parent, text="检测结果")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.result_text = tk.Text(result_frame, height=10, width=30)
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def setup_image_display(self, parent):
        """设置图像显示区域"""
        # 图像显示画布
        self.canvas = tk.Canvas(parent, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 状态栏
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT)
        
        self.coord_var = tk.StringVar(value="坐标: (0, 0)")
        ttk.Label(status_frame, textvariable=self.coord_var).pack(side=tk.RIGHT)
        
        # 绑定鼠标事件
        self.canvas.bind("<Motion>", self.on_mouse_move)
        self.canvas.bind("<Button-1>", self.on_mouse_click)
        
    def setup_logging(self):
        """设置日志记录"""
        self.detector.logger.addHandler(self.TextHandler(self.result_text))
        
    class TextHandler:
        """自定义日志处理器，将日志输出到Text控件"""
        def __init__(self, text_widget):
            self.text_widget = text_widget
            
        def emit(self, record):
            msg = f"[{datetime.now().strftime('%H:%M:%S')}] {record.getMessage()}\n"
            self.text_widget.insert(tk.END, msg)
            self.text_widget.see(tk.END)
            
    def load_image(self):
        """加载图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                self.current_image = cv2.imread(file_path)
                if self.current_image is not None:
                    self.display_image(self.current_image)
                    self.status_var.set(f"已加载图像: {os.path.basename(file_path)}")
                else:
                    messagebox.showerror("错误", "无法加载图像文件")
            except Exception as e:
                messagebox.showerror("错误", f"加载图像失败: {e}")
                
    def load_template(self):
        """加载模板图像"""
        file_path = filedialog.askopenfilename(
            title="选择模板图像",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                self.template_image = cv2.imread(file_path)
                if self.template_image is not None:
                    self.status_var.set(f"已加载模板: {os.path.basename(file_path)}")
                else:
                    messagebox.showerror("错误", "无法加载模板文件")
            except Exception as e:
                messagebox.showerror("错误", f"加载模板失败: {e}")
                
    def start_detection(self):
        """开始检测"""
        if self.current_image is None:
            messagebox.showwarning("警告", "请先加载图像")
            return
            
        # 更新检测参数
        self.update_detection_params()
        
        # 在后台线程中执行检测
        threading.Thread(target=self.run_detection, daemon=True).start()
        
    def update_detection_params(self):
        """更新检测参数"""
        # 更新圆形检测参数
        self.detector.circle_params.update({
            'min_radius': self.min_radius_var.get(),
            'max_radius': self.max_radius_var.get()
        })
        
        # 更新模板匹配参数
        self.detector.template_params.update({
            'match_threshold': self.match_threshold_var.get()
        })
        
    def run_detection(self):
        """执行检测算法"""
        try:
            self.status_var.set("检测中...")
            
            algo_type = self.algo_var.get()
            
            if algo_type == "circle":
                results = self.detector.detect_circular_workpiece(self.current_image)
            elif algo_type == "template":
                if self.template_image is None:
                    messagebox.showwarning("警告", "请先加载模板图像")
                    return
                results = self.detector.detect_template_workpiece(self.current_image, self.template_image)
            elif algo_type == "edge":
                results = self.detector.detect_edge_features(self.current_image)
            else:
                results = []
                
            self.detection_results = results
            
            # 在主线程中更新UI
            self.root.after(0, self.update_detection_results)
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"检测失败: {e}"))
            
    def update_detection_results(self):
        """更新检测结果显示"""
        if self.detection_results:
            # 在图像上绘制检测结果
            result_image = self.draw_detection_results(self.current_image.copy())
            self.display_image(result_image)
            
            # 显示检测统计
            self.status_var.set(f"检测完成，找到 {len(self.detection_results)} 个工件")
            
            # 输出坐标信息
            for i, result in enumerate(self.detection_results):
                coord_str = self.detector.convert_to_robot_coordinates(result)
                self.result_text.insert(tk.END, f"工件 {i+1}: {coord_str}\n")
                
        else:
            self.status_var.set("检测完成，未找到工件")
            
    def draw_detection_results(self, image):
        """在图像上绘制检测结果"""
        for result in self.detection_results:
            x, y = int(result['center_x']), int(result['center_y'])
            
            if result['type'] == 'circle':
                r = int(result['radius'])
                cv2.circle(image, (x, y), r, (0, 255, 0), 2)
                cv2.circle(image, (x, y), 2, (0, 0, 255), 3)
                
            elif result['type'] == 'template':
                w, h = int(result.get('width', 50)), int(result.get('height', 50))
                cv2.rectangle(image, (x-w//2, y-h//2), (x+w//2, y+h//2), (255, 0, 0), 2)
                
            elif result['type'] == 'edge_feature':
                bbox = result.get('bbox', {})
                if bbox:
                    cv2.rectangle(image, (bbox['x'], bbox['y']), 
                                (bbox['x'] + bbox['width'], bbox['y'] + bbox['height']), 
                                (0, 255, 255), 2)
            
            # 添加文本标注
            confidence = result.get('confidence', 0)
            cv2.putText(image, f"{confidence:.2f}", (x-20, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                       
        return image
        
    def display_image(self, image):
        """在画布上显示图像"""
        # 转换颜色空间
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 调整图像大小以适应画布
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width > 1 and canvas_height > 1:
            h, w = image_rgb.shape[:2]
            scale = min(canvas_width/w, canvas_height/h)
            new_w, new_h = int(w*scale), int(h*scale)
            
            image_resized = cv2.resize(image_rgb, (new_w, new_h))
            
            # 转换为PIL图像
            pil_image = Image.fromarray(image_resized)
            self.photo = ImageTk.PhotoImage(pil_image)
            
            # 清除画布并显示图像
            self.canvas.delete("all")
            self.canvas.create_image(canvas_width//2, canvas_height//2, 
                                   image=self.photo, anchor=tk.CENTER)
                                   
    def clear_results(self):
        """清除检测结果"""
        self.detection_results = []
        if self.current_image is not None:
            self.display_image(self.current_image)
        self.result_text.delete(1.0, tk.END)
        self.status_var.set("结果已清除")
        
    def save_results(self):
        """保存检测结果"""
        if not self.detection_results or self.current_image is None:
            messagebox.showwarning("警告", "没有检测结果可保存")
            return
            
        file_path = filedialog.asksaveasfilename(
            title="保存检测结果",
            defaultextension=".jpg",
            filetypes=[("JPEG文件", "*.jpg"), ("PNG文件", "*.png"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                result_image = self.draw_detection_results(self.current_image.copy())
                cv2.imwrite(file_path, result_image)
                
                # 同时保存JSON格式的检测数据
                json_path = file_path.rsplit('.', 1)[0] + '.json'
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(self.detection_results, f, ensure_ascii=False, indent=2)
                    
                messagebox.showinfo("成功", f"结果已保存到:\n{file_path}\n{json_path}")
                
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
                
    def start_tcp_server(self):
        """启动TCP服务器"""
        try:
            self.detector.start_tcp_server()
            self.tcp_status_var.set("服务器运行中")
        except Exception as e:
            messagebox.showerror("错误", f"启动TCP服务器失败: {e}")
            
    def stop_tcp_server(self):
        """停止TCP服务器"""
        try:
            self.detector.stop_tcp_server()
            self.tcp_status_var.set("服务器已停止")
        except Exception as e:
            messagebox.showerror("错误", f"停止TCP服务器失败: {e}")
            
    def on_mouse_move(self, event):
        """鼠标移动事件"""
        self.coord_var.set(f"坐标: ({event.x}, {event.y})")
        
    def on_mouse_click(self, event):
        """鼠标点击事件"""
        # 可以在这里添加交互功能，比如手动标记工件位置
        pass
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    # 创建演示应用
    app = VisionDemoGUI()
    
    try:
        app.run()
    except KeyboardInterrupt:
        print("程序退出")
    finally:
        # 清理资源
        app.detector.stop_tcp_server()
