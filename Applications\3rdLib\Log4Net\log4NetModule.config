﻿<?xml version="1.0" encoding="utf-8" ?>

<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>
  
  <log4net>
    <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">	  
      <!--日志路径-->
      <param name= "File" type="log4net.Util.PatternString" value= "..\..\..\log\Module\%property{LogName}.log"/>
      <!--是否是向文件中追加日志-->
      <param name= "AppendToFile" value= "true"/>
	  <!--编码-->
	  <param name="Encoding" value="utf-8" />
      <!--log保留天数-->
      <param name= "MaxSizeRollBackups" value= "1"/>
      <!--日志文件名是否是固定不变的-->
      <param name= "StaticLogFileName" value= "true"/>
      <!--日志文件名格式为:2008-08-31.log-->
      <param name= "DatePattern" value= "yyyy-MM-dd&quot;.log&quot;"/>	
      <!--日志根据日期滚动-->
      <param name= "RollingStyle" value= "Composite"/>
	  <param name="MaximumFileSize" value="10MB"/>
	  <!--多进程同时写入时加锁-->
	  <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <layout type="log4net.Layout.PatternLayout">
        <!-- <param name="ConversionPattern" value="%d [%t] %-5p %c - %m%n %loggername" /> -->
		         <!-- <conversionPattern value="Time：%date ThreadID:[%thread] Level：%-5level MSG：%message%newlineNUM：%-5L FILE：%F%newline" /> -->
				<!-- <conversionPattern value="%date %-5level  [%thread] %message%newline%newline"/> -->
				<conversionPattern value="%date [%thread] %-5level [%class.%method@%L] %message %newline%newline" />
      </layout>
    </appender>

    <root>
      <!--(高) OFF > FATAL > ERROR > WARN > INFO > DEBUG > ALL (低) -->
      <level value="ERROR" />
	  <appender-ref ref="RollingLogFileAppender"/> 
    </root>
<!-- 	<logger name="VisionMaster">
      <level value="All" />
      <appender-ref ref="RollingLogFileAppender"/>
    </logger> -->
  </log4net>
</configuration>
