<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ICSharpCode.WpfDesign.XamlDom</name>
    </assembly>
    <members>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.CollectionElementsCollection">
            <summary>
            The collection used by XamlProperty.CollectionElements
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.CollectionElementsCollection.AddInternal(ICSharpCode.WpfDesign.XamlDom.XamlPropertyValue)">
            <summary>
            Used by parser to construct the collection without changing the XmlDocument.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.CollectionSupport">
            <summary>
            Static class containing helper methods to work with collections (like the XamlParser does)
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.CollectionSupport.IsCollectionType(System.Type)">
            <summary>
            Gets if the type is considered a collection in XAML.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.CollectionSupport.CanCollectionAdd(System.Type,System.Type)">
            <summary>
            Gets if the collection type <paramref name="col"/> can accepts items of type
            <paramref name="item"/>.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.CollectionSupport.CanCollectionAdd(System.Type,System.Collections.IEnumerable)">
            <summary>
            Gets if the collection type <paramref name="col"/> can accept the specified items.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.CollectionSupport.AddToCollection(System.Type,System.Object,ICSharpCode.WpfDesign.XamlDom.XamlPropertyValue)">
            <summary>
            Adds a value to the end of a collection.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.CollectionSupport.Insert(System.Type,System.Object,ICSharpCode.WpfDesign.XamlDom.XamlPropertyValue,System.Int32)">
            <summary>
            Adds a value at the specified index in the collection.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.CollectionSupport.TryInsert(System.Type,System.Object,ICSharpCode.WpfDesign.XamlDom.XamlPropertyValue,System.Int32)">
            <summary>
            Adds a value at the specified index in the collection. A return value indicates whether the Insert succeeded.
            </summary>
            <returns>True if the Insert succeeded, false if the collection type does not support Insert.</returns>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.CollectionSupport.RemoveItemAt(System.Type,System.Object,System.Int32)">
            <summary>
            Removes the item at the specified index of the collection.
            </summary>
            <returns>True if the removal succeeded, false if the collection type does not support RemoveAt.</returns>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.CollectionSupport.RemoveItem(System.Type,System.Object,System.Object)">
            <summary>
            Removes an item instance from the specified collection.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.CollectionSupport.RemoveItem(System.Type,System.Object,System.Object,ICSharpCode.WpfDesign.XamlDom.XamlPropertyValue)">
            <summary>
            Removes an item instance from the specified collection.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.DesignInstanceExtension">
            <summary>
            A class which implements the DesignInstanceExtension normally defined in the Blend Namespace.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignInstanceExtension.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:ICSharpCode.WpfDesign.XamlDom.DesignInstanceExtension"/> class.
            </summary>
            <param name="type">The type to create.</param>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignInstanceExtension.ProvideValue(System.IServiceProvider)">
            <inheritdoc/>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.DesignInstanceExtension.Type">
            <summary>
            Gets or sets the type to create.
            Use <see cref="P:ICSharpCode.WpfDesign.XamlDom.DesignInstanceExtension.IsDesignTimeCreatable"/> to specify whether an instance of your type or a designer-generated substitute type is created.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.DesignInstanceExtension.IsDesignTimeCreatable">
            <summary>
            Gets or sets if the design instance is created from your type (true) or of a designer-generated substitute type (false). 
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.DesignInstanceExtension.CreateList">
            <summary>
            Gets or sets if the design instance is a list of the specified type. 
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties">
            <summary>
            Helper Class for the Markup Compatibility Properties used by VS and Blend
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.GetClass(System.Windows.DependencyObject)">
            <summary>
            Getter for the <see cref="F:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.ClassProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.SetClass(System.Windows.DependencyObject,System.String)">
            <summary>
            Setter for the <see cref="F:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.ClassProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.ClassProperty">
            <summary>
            Class-Name Property
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.GetClassModifier(System.Windows.DependencyObject)">
            <summary>
            Getter for the <see cref="F:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.ClassModifierProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.SetClassModifier(System.Windows.DependencyObject,System.String)">
            <summary>
            Setter for the <see cref="F:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.ClassModifierProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.ClassModifierProperty">
            <summary>
            Class Modifier Property
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.GetTypeArguments(System.Windows.DependencyObject)">
            <summary>
            Getter for the <see cref="F:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.TypeArgumentsProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.SetTypeArguments(System.Windows.DependencyObject,System.String)">
            <summary>
            Getter for the <see cref="F:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.TypeArgumentsProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlNamespaceProperties.TypeArgumentsProperty">
            <summary>
            Type Arguments Property
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties">
            <summary>
            Helper Class for the Design Time Properties used by VS and Blend
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.GetIsHidden(System.Windows.DependencyObject)">
            <summary>
            Getter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.IsHiddenProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.SetIsHidden(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Setter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.IsHiddenProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.IsHiddenProperty">
            <summary>
            Design-time IsHidden property
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.GetIsLocked(System.Windows.DependencyObject)">
            <summary>
            Getter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.IsLockedProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.SetIsLocked(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Setter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.IsLockedProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.IsLockedProperty">
            <summary>
            Design-time IsLocked property.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.GetDataContext(System.Windows.DependencyObject)">
            <summary>
            Getter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DataContextProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.SetDataContext(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Setter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DataContextProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DataContextProperty">
            <summary>
            Design-time data context
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.GetDesignSource(System.Windows.DependencyObject)">
            <summary>
            Getter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DesignSourceProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.SetDesignSource(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Setter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DesignSourceProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DesignSourceProperty">
            <summary>
            Design-time design source
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.GetDesignWidth(System.Windows.DependencyObject)">
            <summary>
            Getter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DesignWidthProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.SetDesignWidth(System.Windows.DependencyObject,System.Double)">
            <summary>
            Setter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DesignWidthProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DesignWidthProperty">
            <summary>
            Design-time width
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.GetDesignHeight(System.Windows.DependencyObject)">
            <summary>
            Getter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DesignHeightProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.SetDesignHeight(System.Windows.DependencyObject,System.Double)">
            <summary>
            Setter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DesignHeightProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.DesignHeightProperty">
            <summary>
            Design-time height
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.GetLayoutOverrides(System.Windows.DependencyObject)">
            <summary>
            Getter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.LayoutOverridesProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.SetLayoutOverrides(System.Windows.DependencyObject,System.String)">
            <summary>
            Setter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.LayoutOverridesProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.LayoutOverridesProperty">
            <summary>
            Layout-Overrides
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.GetLayoutRounding(System.Windows.DependencyObject)">
            <summary>
            Getter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.LayoutRoundingProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.SetLayoutRounding(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Setter for <see cref="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.LayoutRoundingProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.DesignTimeProperties.LayoutRoundingProperty">
            <summary>
            Design-time layout rounding
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.IXamlErrorSink">
            <summary>
            Interface where errors during XAML loading are reported.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.IXamlErrorSink.ReportError(System.String,System.Int32,System.Int32)">
            <summary>
            Reports a XAML load error.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.MarkupCompatibilityProperties">
            <summary>
            Helper Class for the Markup Compatibility Properties used by VS and Blend
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.MarkupCompatibilityProperties.GetIgnorable(System.Windows.DependencyObject)">
            <summary>
            Getter for the <see cref="F:ICSharpCode.WpfDesign.XamlDom.MarkupCompatibilityProperties.IgnorableProperty"/>
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.MarkupCompatibilityProperties.SetIgnorable(System.Windows.DependencyObject,System.String)">
            <summary>
            Setter for the <see cref="F:ICSharpCode.WpfDesign.XamlDom.MarkupCompatibilityProperties.IgnorableProperty"/>
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.MarkupCompatibilityProperties.IgnorableProperty">
            <summary>
            Gets/Sets whether a XAML namespace may be ignored by the XAML parser.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionTokenizer">
            <summary>
            Tokenizer for markup extension attributes.
            [MS-XAML 6.6.7.1]
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlMarkupExtensionParseException">
            <summary>
            Exception thrown when XAML loading fails because there is a syntax error in a markup extension.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlLoadException">
            <summary>
            Exception class used for xaml loading failures.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlLoadException.#ctor">
            <summary>
            Create a new XamlLoadException instance.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlLoadException.#ctor(System.String)">
            <summary>
            Create a new XamlLoadException instance.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlLoadException.#ctor(System.String,System.Exception)">
            <summary>
            Create a new XamlLoadException instance.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlLoadException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Create a new XamlLoadException instance.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlMarkupExtensionParseException.#ctor">
            <summary>
            Create a new XamlMarkupExtensionParseException instance.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlMarkupExtensionParseException.#ctor(System.String)">
            <summary>
            Create a new XamlMarkupExtensionParseException instance.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlMarkupExtensionParseException.#ctor(System.String,System.Exception)">
            <summary>
            Create a new XamlMarkupExtensionParseException instance.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlMarkupExtensionParseException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Create a new XamlMarkupExtensionParseException instance.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionParser">
            <summary>
            [MS-XAML 6.6.7.2]
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionPrinter">
            <summary>
            Static class that can generate XAML markup extension code ("{Binding Path=...}").
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionPrinter.CanPrint(ICSharpCode.WpfDesign.XamlDom.XamlObject)">
            <summary>
            Gets whether shorthand XAML markup extension code can be generated for the object.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionPrinter.Print(ICSharpCode.WpfDesign.XamlDom.XamlObject)">
            <summary>
            Generates XAML markup extension code for the object.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapper">
            <summary>
            A wrapper for markup extensions if custom behavior of <see cref="M:System.Windows.Markup.MarkupExtension.ProvideValue(System.IServiceProvider)"/> is needed in the designer.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapper.#ctor(ICSharpCode.WpfDesign.XamlDom.XamlObject)">
            <summary>
            Initializes a new instance.
            </summary>
            <param name="xamlObject">The <see cref="P:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapper.XamlObject"/> object that represents the markup extension.</param>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapper.ProvideValue">
            <summary>
            Returns an object that should be used as the value of the target property in the designer. 
            </summary>
            <returns>An object that should be used as the value of the target property in the designer.</returns>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapper.RegisterMarkupExtensionWrapper(System.Type,System.Type)">
            <summary>
            Registers a markup extension wrapper.
            </summary>
            <param name="markupExtensionType">The type of the markup extension.</param>
            <param name="markupExtensionWrapperType">The type of the markup extension wrapper.</param>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapper.XamlObject">
            <summary>
            Gets the <see cref="P:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapper.XamlObject"/> object that represents the markup extension.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapperAttribute">
            <summary>
            Apply this to markup extensions that needs custom behavior of <see cref="M:System.Windows.Markup.MarkupExtension.ProvideValue(System.IServiceProvider)"/> in the designer.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapperAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of <see cref="T:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapperAttribute"/>.
            </summary>
            <param name="markupExtensionWrapperType">The wrapper type.</param>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.MarkupExtensionWrapperAttribute.MarkupExtensionWrapperType">
            <summary>
            Gets the wrapper type.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.NameScopeHelper">
            <summary>
            Static methods to help with <see cref="T:System.Windows.Markup.INameScope"/> operations on Xaml elements.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.NameScopeHelper.NameChanged(ICSharpCode.WpfDesign.XamlDom.XamlObject,System.String,System.String)">
            <summary>
            Finds the XAML namescope for the specified object and uses it to unregister the old name and then register the new name.
            </summary>
            <param name="namedObject">The object where the name was changed.</param>
            <param name="oldName">The old name.</param>
            <param name="newName">The new name.</param>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.NameScopeHelper.GetNameScopeFromObject(ICSharpCode.WpfDesign.XamlDom.XamlObject)">
            <summary>
            Gets the XAML namescope for the specified object.
            </summary>
            <param name="obj">The object to get the XAML namescope for.</param>
            <returns>A XAML namescope, as an <see cref="T:System.Windows.Markup.INameScope"/> instance.</returns>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.NameScopeHelper.ClearNameScopeProperty(System.Object)">
            <summary>
            Clears the <see cref="F:System.Windows.NameScope.NameScopeProperty"/> if the object is a <see cref="T:System.Windows.DependencyObject"/>.
            </summary>
            <param name="obj">The object to clear the <see cref="F:System.Windows.NameScope.NameScopeProperty"/> on.</param>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.PositionXmlDocument">
            <summary>
            Class derived from System.Xml.XmlDocument that remembers line/column information for elements and attributes
            when loading from a <see cref="T:System.Xml.XmlTextReader"/> or other <see cref="T:System.Xml.IXmlLineInfo"/>-implementing reader.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.PositionXmlDocument.CreateElement(System.String,System.String,System.String)">
            <summary>
            Creates a PositionXmlElement.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.PositionXmlDocument.CreateAttribute(System.String,System.String,System.String)">
            <summary>
            Creates a PositionXmlAttribute.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.PositionXmlDocument.Load(System.Xml.XmlReader)">
            <summary>
            Loads the XML document from the specified <see cref="T:System.Xml.XmlReader"/>.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.PositionXmlElement">
            <summary>
            XML Element with line/column information.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.PositionXmlElement.HasLineInfo">
            <summary>
            Gets whether the element has line information.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.PositionXmlElement.LineNumber">
            <summary>
            Gets the line number.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.PositionXmlElement.LinePosition">
            <summary>
            Gets the line position (column).
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.PositionXmlElement.XamlElementLineInfo">
            <summary>
            Get the XamlElementLineInfo
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.PositionXmlAttribute">
            <summary>
            XML Attribute with line/column information.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.PositionXmlAttribute.HasLineInfo">
            <summary>
            Gets whether the element has line information.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.PositionXmlAttribute.LineNumber">
            <summary>
            Gets the line number.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.PositionXmlAttribute.LinePosition">
            <summary>
            Gets the line position (column).
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.TemplateHelper">
            <summary>
            Contains template related helper methods.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.TemplateHelper.GetFrameworkTemplate(System.Xml.XmlElement,ICSharpCode.WpfDesign.XamlDom.XamlObject)">
            <summary>
            Gets a <see cref="T:System.Windows.FrameworkTemplate"/> based on the specified parameters.
            </summary>
            <param name="xmlElement">The xml element to get template xaml from.</param>
            <param name="parentObject">The <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlObject"/> to use as source for resources and contextual information.</param>
            <returns>A <see cref="T:System.Windows.FrameworkTemplate"/> based on the specified parameters.</returns>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlConstants">
            <summary>
            Contains constants used by the Xaml parser.
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlConstants.XmlnsNamespace">
            <summary>
            The namespace used to identify "xmlns".
            Value: "http://www.w3.org/2000/xmlns/"
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlConstants.XamlNamespace">
            <summary>
            The namespace used for the XAML schema.
            Value: "http://schemas.microsoft.com/winfx/2006/xaml"
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlConstants.Xaml2009Namespace">
            <summary>
            The namespace used for the 2009 XAML schema.
            Value: "http://schemas.microsoft.com/winfx/2009/xaml"
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlConstants.PresentationNamespace">
            <summary>
            The namespace used for the WPF schema.
            Value: "http://schemas.microsoft.com/winfx/2006/xaml/presentation"
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlConstants.DesignTimeNamespace">
            <summary>
            The namespace used for the DesignTime schema.
            Value: "http://schemas.microsoft.com/expression/blend/2008"
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlConstants.MarkupCompatibilityNamespace">
            <summary>
            The namespace used for the MarkupCompatibility schema.
            Value: "http://schemas.openxmlformats.org/markup-compatibility/2006"
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlConstants.ResourcesPropertyName">
            <summary>
            The name of the Resources property.
            Value: "Resources"
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlConstants.Xmlns">
            <summary>
            The name of xmlns.
            Value: "xmlns"
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlDocument">
            <summary>
            Represents a .xaml document.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlDocument.GetTypeDescriptorContext(ICSharpCode.WpfDesign.XamlDom.XamlObject)">
            <summary>
            Gets the type descriptor context used for type conversions.
            </summary>
            <param name="containingObject">The containing object, used when the
            type descriptor context needs to resolve an XML namespace.</param>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlDocument.Save(System.Xml.XmlWriter)">
            <summary>
            Saves the xaml document into the <paramref name="writer"/>.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlDocument.#ctor(System.Xml.XmlDocument,ICSharpCode.WpfDesign.XamlDom.XamlParserSettings)">
            <summary>
            Internal constructor, used by XamlParser.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlDocument.ParseComplete(ICSharpCode.WpfDesign.XamlDom.XamlObject)">
            <summary>
            Called by XamlParser to finish initializing the document.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlDocument.CreateObject(System.Object)">
            <summary>
            Create an XamlObject from the instance.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlDocument.CreateNullValue">
            <summary>
            Creates a value that represents {x:Null}
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlDocument.CreatePropertyValue(System.Object,ICSharpCode.WpfDesign.XamlDom.XamlProperty)">
            <summary>
            Create a XamlPropertyValue for the specified value instance.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlDocument.TypeFinder">
            <summary>
            Gets the type finder used for this XAML document.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlDocument.ServiceProvider">
            <summary>
            Gets the service provider used for markup extensions in this document.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlDocument.CurrentProjectAssemblyName">
            <summary>
            Gets the Current Projects Assembly Name (if it has any).
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlDocument.RootElement">
            <summary>
            Gets the root xaml object.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlDocument.RootInstance">
            <summary>
            Gets the object instance created by the root xaml object.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlObject">
            <summary>
            Represents a xaml object element.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlPropertyValue">
            <summary>
            Used for the value of a <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlProperty"/>.
            Can be a <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlTextValue"/> or a <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlObject"/>.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlPropertyValue.GetValueFor(ICSharpCode.WpfDesign.XamlDom.XamlPropertyInfo)">
            <summary>
            used internally by the XamlParser.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlPropertyValue.ParentProperty">
            <summary>
            Gets the parent property that this value is assigned to.
            </summary>
        </member>
        <member name="E:ICSharpCode.WpfDesign.XamlDom.XamlPropertyValue.ParentPropertyChanged">
            <summary>
            Occurs when the value of the ParentProperty property changes.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObject.#ctor(ICSharpCode.WpfDesign.XamlDom.XamlDocument,System.Xml.XmlElement,System.Type,System.Object)">
            <summary>For use by XamlParser only.</summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObject.AddProperty(ICSharpCode.WpfDesign.XamlDom.XamlProperty)">
            <summary>For use by XamlParser only.</summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObject.GetContentPropertyName(System.Type)">
            <summary>
            Gets the name of the content property for the specified element type, or null if not available.
            </summary>
            <param name="elementType">The element type to get the content property name for.</param>
            <returns>The name of the content property for the specified element type, or null if not available.</returns>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObject.FindOrCreateProperty(System.String)">
            <summary>
            Finds the specified property, or creates it if it doesn't exist.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObject.FindOrCreateAttachedProperty(System.Type,System.String)">
            <summary>
            Finds the specified property, or creates it if it doesn't exist.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObject.GetXamlAttribute(System.String)">
            <summary>
            Gets an attribute in the x:-namespace.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObject.SetXamlAttribute(System.String,System.String)">
            <summary>
            Sets an attribute in the x:-namespace.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.ParentObject">
            <summary>
            Gets the parent object.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.SystemXamlTypeForProperty">
            <summary>
            Gets a <see cref="T:System.Xaml.XamlType"/> representing the <see cref="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.ElementType"/>.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.OwnerDocument">
            <summary>
            Gets the XamlDocument where this XamlObject is declared in.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.Instance">
            <summary>
            Gets the instance created by this object element.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.IsMarkupExtension">
            <summary>
            Gets whether this instance represents a MarkupExtension.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.HasErrors">
            <summary>
            Gets whether there were load errors for this object.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.ElementType">
            <summary>
            Gets the type of this object element.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.Properties">
            <summary>
            Gets a read-only collection of properties set on this XamlObject.
            This includes both attribute and element properties.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.ContentPropertyName">
            <summary>
            Gets the name of the content property.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.RuntimeNameProperty">
            <summary>
            Gets which property name of the type maps to the XAML x:Name attribute.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.NameProperty">
            <summary>
            Gets which property of the type maps to the XAML x:Name attribute.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.Name">
            <summary>
            Gets/Sets the name of this XamlObject.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObject.ServiceProvider">
            <summary>
            Gets/Sets the <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider"/> associated with this XamlObject.
            </summary>
        </member>
        <member name="E:ICSharpCode.WpfDesign.XamlDom.XamlObject.NameChanged">
            <summary>
            Is raised when the name of this XamlObject changes.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider">
            <summary>
            A service provider that provides the IProvideValueTarget and IXamlTypeResolver services.
            No other services (e.g. from the document's service provider) are offered.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.#ctor(ICSharpCode.WpfDesign.XamlDom.XamlObject)">
            <summary>
            Creates a new XamlObjectServiceProvider instance.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.GetService(System.Type)">
            <summary>
            Retrieves the service of the specified type.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.GetFirstAmbientValue(System.Collections.Generic.IEnumerable{System.Xaml.XamlType},System.Xaml.XamlMember[])">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.GetFirstAmbientValue(System.Xaml.XamlType[])">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.GetAllAmbientValues(System.Collections.Generic.IEnumerable{System.Xaml.XamlType},System.Xaml.XamlMember[])">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.GetAllAmbientValues(System.Xaml.XamlType[])">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.GetAllAmbientValues(System.Collections.Generic.IEnumerable{System.Xaml.XamlType},System.Boolean,System.Collections.Generic.IEnumerable{System.Xaml.XamlType},System.Xaml.XamlMember[])">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.Resolve(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.Resolve(System.String,System.Boolean@)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.GetFixupToken(System.Collections.Generic.IEnumerable{System.String})">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.GetFixupToken(System.Collections.Generic.IEnumerable{System.String},System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.GetAllNamesAndValuesInScope">
            <inheritdoc/>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.XamlObject">
            <summary>
            Gets the XamlObject that owns this service provider (e.g. the XamlObject that represents a markup extension).
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.TargetObject">
            <summary>
            Gets the target object (the DependencyObject instance on which a property should be set)
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.TargetProperty">
            <summary>
            Gets the target dependency property.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.BaseUri">
            <inheritdoc/>		
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.SchemaContext">
            <inheritdoc/>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.IsFixupTokenAvailable">
            <inheritdoc/>
        </member>
        <member name="E:ICSharpCode.WpfDesign.XamlDom.XamlObjectServiceProvider.OnNameScopeInitializationComplete">
            <inheritdoc/>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlParser">
            <summary>
            Class with static methods to parse XAML files and output a <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlDocument"/>.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlParser.Parse(System.IO.Stream)">
            <summary>
            Parses a XAML document using a stream.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlParser.Parse(System.IO.TextReader)">
            <summary>
            Parses a XAML document using a TextReader.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlParser.Parse(System.Xml.XmlReader)">
            <summary>
            Parses a XAML document using an XmlReader.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlParser.Parse(System.IO.Stream,ICSharpCode.WpfDesign.XamlDom.XamlParserSettings)">
            <summary>
            Parses a XAML document using a stream.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlParser.Parse(System.IO.TextReader,ICSharpCode.WpfDesign.XamlDom.XamlParserSettings)">
            <summary>
            Parses a XAML document using a TextReader.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlParser.Parse(System.Xml.XmlReader,ICSharpCode.WpfDesign.XamlDom.XamlParserSettings)">
            <summary>
            Parses a XAML document using an XmlReader.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlParser.Parse(System.Xml.XmlDocument,ICSharpCode.WpfDesign.XamlDom.XamlParserSettings)">
            <summary>
            Creates a XAML document from an existing XmlDocument.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlParser.RemoveRootNamespacesFromNodeAndChildNodes(ICSharpCode.WpfDesign.XamlDom.XamlObject,System.Xml.XmlNode)">
            <summary>
            Removes namespace attributes defined in the root from the specified node and all child nodes.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlParser.ParseSnippet(ICSharpCode.WpfDesign.XamlDom.XamlObject,System.String,ICSharpCode.WpfDesign.XamlDom.XamlParserSettings)">
            <summary>
            Method use to parse a piece of Xaml.
            </summary>
            <param name="root">The Root XamlObject of the current document.</param>
            <param name="xaml">The Xaml being parsed.</param>
            <param name="settings">Parser settings used by <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlParser"/>.</param>
            <returns>Returns the XamlObject of the parsed <paramref name="xaml"/>.</returns>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlParser.ParseSnippet(ICSharpCode.WpfDesign.XamlDom.XamlObject,System.String,ICSharpCode.WpfDesign.XamlDom.XamlParserSettings,ICSharpCode.WpfDesign.XamlDom.XamlObject)">
            <summary>
            Method use to parse a piece of Xaml.
            </summary>
            <param name="root">The Root XamlObject of the current document.</param>
            <param name="xaml">The Xaml being parsed.</param>
            <param name="settings">Parser settings used by <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlParser"/>.</param>
            <param name="parentObject">Parent Object, where the Parsed snippet will be inserted (Needed for Example for Bindings).</param>
            <returns>Returns the XamlObject of the parsed <paramref name="xaml"/>.</returns>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.CreateInstanceCallback">
            <summary>
            Delegate used for XamlParserSettings.CreateInstanceCallback.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlParserSettings">
            <summary>
            Settings used for the XamlParser.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlParserSettings.CreateInstanceCallback">
            <summary>
            Gets/Sets the method used to create object instances.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlParserSettings.TypeFinder">
            <summary>
            Gets/Sets the type finder to do type lookup.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlParserSettings.ServiceProvider">
            <summary>
            Gets/Sets the service provider to use to initialize markup extensions.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlParserSettings.CurrentProjectAssemblyName">
            <summary>
            Gets/Sets the Current Projects Assembly Name.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlProperty">
            <summary>
            Describes a property on a <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlObject"/>.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlProperty.Reset">
            <summary>
            Resets the properties value.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlProperty.ParserAddCollectionElement(System.Xml.XmlElement,ICSharpCode.WpfDesign.XamlDom.XamlPropertyValue)">
            <summary>
            used internally by the XamlParser.
            Add a collection element that already is part of the XML DOM.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.ParentObject">
            <summary>
            Gets the parent object for which this property was declared.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.PropertyName">
            <summary>
            Gets the property name.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.PropertyTargetType">
            <summary>
            Gets the type the property is declared on.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.IsAttached">
            <summary>
            Gets if this property is an attached property.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.IsEvent">
            <summary>
            Gets if this property is an event.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.ReturnType">
            <summary>
            Gets the return type of the property.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.TypeConverter">
            <summary>
            Gets the type converter used to convert property values to/from string.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.Category">
            <summary>
            Gets the category of the property.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.PropertyValue">
            <summary>
            Gets the value of the property. Can be null if the property is a collection property.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.IsResources">
            <summary>
            Gets if the property represents the FrameworkElement.Resources property that holds a locally-defined resource dictionary.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.IsCollection">
            <summary>
            Gets if the property is a collection property.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.CollectionElements">
            <summary>
            Gets the collection elements of the property. Is empty if the property is not a collection.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.IsSet">
            <summary>
            Gets if the property is set.
            </summary>
        </member>
        <member name="E:ICSharpCode.WpfDesign.XamlDom.XamlProperty.IsSetChanged">
            <summary>
            Occurs when the value of the IsSet property has changed.
            </summary>
        </member>
        <member name="E:ICSharpCode.WpfDesign.XamlDom.XamlProperty.ValueChanged">
            <summary>
            Occurs when the value of the property has changed.
            </summary>
        </member>
        <member name="E:ICSharpCode.WpfDesign.XamlDom.XamlProperty.ValueOnInstanceChanged">
            <summary>
            Occurs when MarkupExtension evaluated PropertyValue dosn't changed but ValueOnInstance does.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.SystemXamlMemberForProperty">
            <summary>
            Gets a <see cref="T:System.Xaml.XamlMember"/> representing the property. 
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.SystemXamlTypeForProperty">
            <summary>
            Gets a <see cref="T:System.Xaml.XamlType"/> representing the type the property is declared on. 
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.ValueOnInstance">
            <summary>
            Gets/Sets the value of the property on the instance without updating the XAML document.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.TextValueOnInstance">
            <summary>
            Gets the value of the text property on the instance without updating the XAML document.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.IsAdvanced">
            <summary>
            Gets if this property is considered "advanced" and should be hidden by default in a property grid.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlProperty.DependencyProperty">
            <summary>
            Gets the dependency property.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlPropertyInfo">
            <summary>
            Represents a property assignable in XAML.
            This can be a normal .NET property or an attached property.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlStaticTools">
            <summary>
            Static methods to help with designer operations which require access to internal Xaml elements.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlStaticTools.GetXaml(ICSharpCode.WpfDesign.XamlDom.XamlObject)">
            <summary>
            Gets the Xaml string of the <paramref name="xamlObject"/>
            </summary>
            <param name="xamlObject">The object whose Xaml is requested.</param>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlTextValue">
            <summary>
            A textual value in a .xaml file.
            </summary>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlTextValue.Text">
            <summary>
            The text represented by the value.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder">
            <summary>
            Allows finding types in a set of assemblies.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.GetType(System.String,System.String)">
            <summary>
            Gets a type referenced in XAML.
            </summary>
            <param name="xmlNamespace">The XML namespace to use to look up the type.
            This can be a registered namespace or a 'clr-namespace' value.</param>
            <param name="localName">The local name of the type to find.</param>
            <returns>
            The requested type, or null if it could not be found.
            </returns>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.GetXmlNamespaceFor(System.Reflection.Assembly,System.String,System.Boolean)">
            <summary>
            Gets the XML namespace that can be used for the specified assembly/namespace combination.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.GetXmlNamespacesFor(System.Reflection.Assembly,System.String,System.Boolean)">
            <summary>
            Gets the XML namespaces that can be used for the specified assembly/namespace combination.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.GetPrefixForXmlNamespace(System.String)">
            <summary>
            Gets the prefix to use for the specified XML namespace,
            or null if no suitable prefix could be found.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.RegisterAssembly(System.Reflection.Assembly)">
            <summary>
            Registers XAML namespaces defined in the <paramref name="assembly"/> for lookup.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.RegisterDesignerNamespaces">
            <summary>
            Register the Namspaces not found in any Assembly, but used by VS and Expression Blend
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.LoadAssembly(System.String)">
            <summary>
            Load the assembly with the specified name.
            You can override this method to implement custom assembly lookup.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.Clone">
            <summary>
            Clones this XamlTypeFinder.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.ImportFrom(ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder)">
            <summary>
            Import information from another XamlTypeFinder.
            Use this if you override Clone().
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.CreateWpfTypeFinder">
            <summary>
            Creates a new XamlTypeFinder where the WPF namespaces are registered.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlTypeFinder.ConvertUriToLocalUri(System.Uri)">
            <summary>
            Converts the specified <see cref="T:System.Uri"/> to local.
            </summary>
        </member>
        <member name="T:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter">
            <summary>
            A special XamlXmlWriter wich fixes &amp; and &quot; in MarkupExtensions where not correctly handled.
            </summary>
        </member>
        <member name="F:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.xmlWriter">
            <summary>
            The <see cref="T:System.Xml.XmlWriter"/> instance used internally.
            </summary>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.#ctor(System.Text.StringBuilder)">
            <summary>
            Initializes a new instance of the <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter"/> class.
            </summary>
            <param name="stringBuilder">The <see cref="T:System.Text.StringBuilder"/> to which to write to.</param>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.#ctor(System.Text.StringBuilder,System.Xml.XmlWriterSettings)">
            <summary>
            Initializes a new instance of the <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter"/> class.
            </summary>
            <param name="stringBuilder">The <see cref="T:System.Text.StringBuilder"/> to which to write to.</param>
            <param name="settings">The <see cref="T:System.Xml.XmlWriterSettings"/> object used to configure the new <see cref="T:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter"/> instance.</param>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteStartDocument">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteStartDocument(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteEndDocument">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteDocType(System.String,System.String,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteStartElement(System.String,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteEndElement">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteFullEndElement">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteStartAttribute(System.String,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteEndAttribute">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteCData(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteComment(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteProcessingInstruction(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteEntityRef(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteCharEntity(System.Char)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteWhitespace(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteString(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteSurrogateCharEntity(System.Char,System.Char)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteChars(System.Char[],System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteRaw(System.Char[],System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteRaw(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteBase64(System.Byte[],System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.Close">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.Flush">
            <inheritdoc/>
        </member>
        <member name="M:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.LookupPrefix(System.String)">
            <inheritdoc/>
        </member>
        <member name="P:ICSharpCode.WpfDesign.XamlDom.XamlXmlWriter.WriteState">
            <inheritdoc/>
        </member>
    </members>
</doc>
